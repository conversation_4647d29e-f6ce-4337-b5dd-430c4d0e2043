import os
from dotenv import load_dotenv

load_dotenv()
APP_PORT = int(os.getenv("APP_PORT", 8000))

# ClickHouse
CLICKHOUSE_HOST = os.getenv("CLICKHOUSE_HOST")
CLICKHOUSE_PORT = int(os.getenv("CLICKHOUSE_PORT", 8123))
CLICKHOUSE_USER = os.getenv("CLICKHOUSE_USER")
CLICKHOUSE_PASSWORD = os.getenv("CLICKHOUSE_PASSWORD")
CLICKHOUSE_DB = os.getenv("CLICKHOUSE_DB")

# Redis Logs
REDISLOGS_HOST = os.getenv("REDISLOGS_HOST")
REDISLOGS_PORT = int(os.getenv("REDISLOGS_PORT"))
REDISLOGS_PASSWORD = os.getenv("REDISLOGS_PASSWORD")

# OpenAI
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
