from langgraph.graph import StateGraph, END
from typing import TypedDict
from app.agent.node_tools import query_node, summary_node
from app.agent.prompt_template import detect_intent


class AgentState(TypedDict, total=False):
    input: str
    sql: str
    table: list[dict]
    summary: str


def build_graph():
    builder = StateGraph(AgentState)

    builder.add_node("router", lambda x: x)
    builder.add_node("query", query_node)
    builder.add_node("summary", summary_node)

    def router(state: AgentState) -> str:
        intent = detect_intent(state["input"])
        print(f"🔀 [ROUTER] Detected intent: {intent}")
        return "query" if intent == "query" else "summary"

    builder.add_conditional_edges(
        "router", router, {"query": "query", "summary": "summary"}
    )

    builder.add_edge("query", "summary")
    builder.add_edge("summary", END)
    builder.set_entry_point("router")

    return builder.compile()
