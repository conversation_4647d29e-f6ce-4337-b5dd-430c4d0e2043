from langgraph.graph import StateGraph, END
from typing import TypedDict, Optional, Dict, Any
from app.agent.node_tools import query_node, summary_node
from app.agent.prompt_template import detect_intent
from app.core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>r, ErrorCode
from app.core.memory_manager import MemoryManager
from app.core.router import AdvancedRouter
import logging

logger = logging.getLogger(__name__)


def error_handler_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """Handle errors and provide user-friendly responses."""
    error_info = state.get("error", {})

    if error_info:
        # Extract user-friendly message
        user_message = error_info.get("error", {}).get("user_message",
                                                      "<PERSON><PERSON>, terjadi kesalahan sistem. Silakan coba lagi nanti.")
        state["summary"] = user_message
        logger.error(f"Error handled: {error_info}")
    else:
        state["summary"] = "Ter<PERSON><PERSON> kesalahan yang tidak diketahui."

    return state


def greeting_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """Handle greeting messages."""
    user_input = state.get("input", "").lower()

    greetings = {
        "pagi": "Selamat pagi! Saya siap membantu Anda menganalisis data FMCG. Apa yang ingin Anda ketahui?",
        "siang": "Selamat siang! Ada data apa yang ingin Anda analisis hari ini?",
        "sore": "Selamat sore! Bagaimana saya bisa membantu analisis data Anda?",
        "malam": "Selamat malam! Saya siap membantu analisis data Anda.",
    }

    # Check for time-specific greetings
    response = "Halo! Saya adalah asisten analisis data FMCG. Saya dapat membantu Anda menganalisis data penjualan, tren, dan metrik bisnis lainnya. Silakan tanyakan apa yang ingin Anda ketahui!"

    for time_word, greeting in greetings.items():
        if time_word in user_input:
            response = greeting
            break

    state["summary"] = response
    return state


def help_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """Provide help and guidance to users."""
    help_text = """
🤖 **Bantuan Asisten Data FMCG**

Saya dapat membantu Anda dengan:

📊 **Analisis Data:**
- Tampilkan data penjualan per bulan/kategori/brand
- Analisis tren dan performa produk
- Ringkasan dan insight dari data

💡 **Contoh Pertanyaan:**
- "Tampilkan penjualan bulan ini"
- "Data penjualan per kategori tahun lalu"
- "Tren penjualan 6 bulan terakhir"
- "Ringkas data penjualan terbaru"

📋 **Format Output:**
- Ringkasan dalam bahasa Indonesia
- Tabel data (ketik "tabel" atau "table")
- Insight dan analisis otomatis

Silakan tanyakan apa yang ingin Anda ketahui tentang data FMCG Anda!
    """

    state["summary"] = help_text.strip()
    return state


class AgentState(TypedDict, total=False):
    input: str
    sql: str
    table: list[dict]
    summary: str
    user_id: Optional[str]
    session_id: Optional[str]
    timestamp: Optional[str]
    intent: Optional[str]
    confidence: Optional[float]
    context: Optional[Dict[str, Any]]
    error: Optional[Dict[str, Any]]


def build_graph():
    """Build the enhanced agent graph with error handling, memory, and advanced routing."""
    builder = StateGraph(AgentState)

    # Initialize core components
    memory_manager = MemoryManager()
    advanced_router = AdvancedRouter(memory_manager)

    # Add nodes
    builder.add_node("router", lambda x: x)
    builder.add_node("query", query_node)
    builder.add_node("summary", summary_node)
    builder.add_node("error_handler", error_handler_node)
    builder.add_node("greeting", greeting_node)
    builder.add_node("help", help_node)

    def enhanced_router(state: Dict[str, Any]) -> str:
        """Enhanced router with advanced intent classification and error handling."""
        try:
            user_input = state.get("input", "")
            session_id = state.get("session_id")
            user_id = state.get("user_id")

            if not user_input:
                raise AgentError(
                    message="Empty input provided",
                    error_code=ErrorCode.INVALID_INPUT
                )

            # Route using advanced router
            target_node, classification = advanced_router.route_request(
                user_input, session_id, user_id
            )

            # Update state with routing information
            state["intent"] = classification.intent.value
            state["confidence"] = classification.confidence
            state["context"] = {
                "routing_reasoning": classification.reasoning,
                "routing_metadata": classification.metadata
            }

            logger.info(
                f"🔀 [ENHANCED ROUTER] Input: '{user_input[:50]}...' -> "
                f"Intent: {classification.intent.value} "
                f"(confidence: {classification.confidence:.2f}) -> "
                f"Route: {target_node}"
            )

            return target_node

        except Exception as e:
            logger.error(f"Router error: {e}")
            # Store error in state for error handler
            state["error"] = error_handler.handle_error(e, context={"node": "router"})
            return "error_handler"

    # Define routing edges with all possible targets
    routing_targets = {
        "query": "query",
        "summary": "summary",
        "greeting": "greeting",
        "help": "help",
        "clarification": "help",  # Route clarification to help
        "follow_up": "query",     # Route follow-ups to query
        "unknown": "query",       # Default fallback
        "error_handler": "error_handler"
    }

    builder.add_conditional_edges("router", enhanced_router, routing_targets)

    # Add edges for normal flow
    builder.add_edge("query", "summary")
    builder.add_edge("summary", END)
    builder.add_edge("greeting", END)
    builder.add_edge("help", END)
    builder.add_edge("error_handler", END)

    builder.set_entry_point("router")

    return builder.compile()
