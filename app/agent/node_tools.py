from app.tools.catalog import fetch_catalog
from app.tools.sql_generator import generate_sql
from app.tools.clickhouse import run_query
from app.tools.summarizer import summarize_table
from app.redis_logs import log_session


def query_node(state):
    prompt = state["input"]
    session = {
        "user_id": state.get("user_id"),
        "session_id": state.get("session_id"),
        "timestamp": state.get("timestamp"),
    }

    catalog = fetch_catalog()
    sql = generate_sql(prompt, catalog)
    table = run_query(sql)

    print(f"📦 [QUERY RESULT] {len(table)} rows")

    state["catalog"] = catalog  # optional: untuk debugging downstream
    state["sql"] = sql
    state["table"] = table

    # Simpan log/memory dalam JSON (single-encode)
    log_session(
        {
            "type": "query",
            "prompt": prompt,
            "catalog": catalog,
            "sql": sql,
            "row_count": len(table),
            "session": session,
        }
    )
    return state


def summary_node(state):
    prompt = state.get("input", "")
    table = state.get("table", [])
    summary = summarize_table(table, prompt=prompt)
    state["summary"] = summary

    log_session(
        {
            "type": "summary",
            "prompt": prompt,
            "summary": summary,
            "session": {
                "user_id": state.get("user_id"),
                "session_id": state.get("session_id"),
                "timestamp": state.get("timestamp"),
            },
        }
    )
    return state
