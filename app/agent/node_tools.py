from app.tools.catalog import fetch_catalog
from app.tools.sql_generator import generate_sql
from app.tools.clickhouse import run_query
from app.tools.summarizer import summarize_table
from app.redis_logs import log_session
from app.core.error_handler import Agent<PERSON><PERSON>r, ErrorCode, error_handler, validate_input
from app.core.memory_manager import memory_manager
from typing import Dict, Any
import logging


logger = logging.getLogger(__name__)

def query_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute database query node with comprehensive error handling.

    Args:
        state: Agent state containing input and session information

    Returns:
        Updated state with query results

    Raises:
        AgentError: If query execution fails
    """
    try:
        # Validate input state
        validate_input(
            state,
            required_fields=["input"],
            field_types={"input": str}
        )

        prompt = state["input"]
        session = {
            "user_id": state.get("user_id"),
            "session_id": state.get("session_id"),
            "timestamp": state.get("timestamp"),
        }

        logger.info(f"🔍 [QUERY NODE] Processing prompt: {prompt[:100]}...")

        # Fetch catalog with error handling
        try:
            catalog = fetch_catalog()
        except Exception as e:
            raise AgentError(
                message=f"Failed to fetch database catalog: {str(e)}",
                error_code=ErrorCode.DATABASE_CONNECTION_ERROR,
                original_exception=e
            )

        # Generate SQL with error handling
        sql = generate_sql(prompt, catalog)

        # Execute query with error handling
        table = run_query(sql)

        logger.info(f"📦 [QUERY RESULT] {len(table)} rows returned")

        # Update state
        state["catalog"] = catalog  # optional: for debugging downstream
        state["sql"] = sql
        state["table"] = table

        # Store in memory system and log session
        try:
            # Store conversation turn in memory
            if session.get("session_id") and session.get("user_id"):
                memory_manager.store_conversation_turn(
                    session_id=session["session_id"],
                    user_id=session["user_id"],
                    user_input=prompt,
                    agent_response=f"Query executed: {len(table)} rows returned",
                    intent=state.get("intent", "query"),
                    sql_query=sql,
                    data_summary=f"{len(table)} rows from database",
                    metadata={
                        "catalog_tables": len(catalog.get("tables", [])),
                        "catalog_views": len(catalog.get("views", [])),
                        "confidence": state.get("confidence", 0.0)
                    }
                )

            # Legacy logging for compatibility
            log_session({
                "type": "query",
                "prompt": prompt,
                "catalog": catalog,
                "sql": sql,
                "row_count": len(table),
                "session": session,
            })
        except Exception as log_error:
            logger.warning(f"Failed to log session (continuing): {log_error}")

        return state

    except AgentError:
        # Re-raise AgentErrors as-is
        raise
    except Exception as e:
        # Convert unexpected errors to AgentError
        raise AgentError(
            message=f"Query node execution failed: {str(e)}",
            error_code=ErrorCode.UNKNOWN_ERROR,
            details={"state_keys": list(state.keys()) if isinstance(state, dict) else "invalid_state"},
            original_exception=e
        )


def summary_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute summary node with comprehensive error handling.

    Args:
        state: Agent state containing table data and input

    Returns:
        Updated state with summary

    Raises:
        AgentError: If summarization fails
    """
    try:
        # Validate input state
        validate_input(
            state,
            required_fields=["input"],
            field_types={"input": str}
        )

        prompt = state.get("input", "")
        table = state.get("table", [])

        logger.info(f"📝 [SUMMARY NODE] Summarizing {len(table)} rows with prompt: {prompt[:100]}...")

        # Validate table data if present
        if table and not isinstance(table, list):
            raise AgentError(
                message="Invalid table format: expected list of dictionaries",
                error_code=ErrorCode.VALIDATION_ERROR,
                details={"table_type": type(table).__name__}
            )

        # Generate summary with error handling
        summary = summarize_table(table, prompt=prompt)
        state["summary"] = summary

        logger.info(f"📝 [SUMMARY RESULT] Generated summary: {len(summary)} characters")

        # Store in memory system and log session
        try:
            # Store conversation turn in memory
            session_id = state.get("session_id")
            user_id = state.get("user_id")

            if session_id and user_id:
                memory_manager.store_conversation_turn(
                    session_id=session_id,
                    user_id=user_id,
                    user_input=prompt,
                    agent_response=summary,
                    intent=state.get("intent", "summary"),
                    sql_query=state.get("sql"),
                    data_summary=summary,
                    metadata={
                        "table_rows": len(table),
                        "summary_length": len(summary),
                        "confidence": state.get("confidence", 0.0)
                    }
                )

            # Legacy logging for compatibility
            log_session({
                "type": "summary",
                "prompt": prompt,
                "summary": summary,
                "session": {
                    "user_id": state.get("user_id"),
                    "session_id": state.get("session_id"),
                    "timestamp": state.get("timestamp"),
                },
            })
        except Exception as log_error:
            logger.warning(f"Failed to log session (continuing): {log_error}")

        return state

    except AgentError:
        # Re-raise AgentErrors as-is
        raise
    except Exception as e:
        # Convert unexpected errors to AgentError
        raise AgentError(
            message=f"Summary node execution failed: {str(e)}",
            error_code=ErrorCode.SUMMARIZATION_ERROR,
            details={"state_keys": list(state.keys()) if isinstance(state, dict) else "invalid_state"},
            original_exception=e
        )
