"""
Legacy intent detection for backward compatibility.
The new system uses app.core.router.AdvancedRouter for sophisticated intent classification.
"""

from app.core.router import AdvancedRouter, IntentType
import logging

logger = logging.getLogger(__name__)

# Global router instance for legacy compatibility
_legacy_router = AdvancedRouter()

def detect_intent(prompt: str) -> str:
    """
    Legacy intent detection function for backward compatibility.

    Note: New code should use AdvancedRouter directly for better features.
    """
    try:
        classification = _legacy_router.classify_intent(prompt)

        # Map new intent types to legacy string format
        intent_mapping = {
            IntentType.QUERY: "query",
            IntentType.SUMMARY: "summary",
            IntentType.GREETING: "greeting",
            IntentType.HELP: "help",
            IntentType.CLARIFICATION: "help",
            IntentType.FOLLOW_UP: "query",
            IntentType.UNKNOWN: "query"
        }

        legacy_intent = intent_mapping.get(classification.intent, "query")

        logger.debug(
            f"Legacy intent detection: '{prompt[:50]}...' -> "
            f"{classification.intent.value} (confidence: {classification.confidence:.2f}) -> "
            f"legacy: {legacy_intent}"
        )

        return legacy_intent

    except Exception as e:
        logger.error(f"Legacy intent detection failed: {e}")
        return "query"  # Safe fallback
