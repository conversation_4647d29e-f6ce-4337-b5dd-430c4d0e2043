def detect_intent(prompt: str) -> str:
    prompt_lower = prompt.lower()

    # Kata-kata yang menunjukkan perlu query/data dulu
    if any(
        word in prompt_lower
        for word in [
            "tampilkan",
            "data",
            "grafik",
            "penjualan",
            "sales",
            "berdasarkan",
            "per bulan",
            "trend",
            "tren",
        ]
    ):
        return "query"

    # Jika user jelas minta ringkasan tanpa konteks data
    if "ringkas" in prompt_lower or "summary" in prompt_lower:
        return "summary"

    return "query"  # default
