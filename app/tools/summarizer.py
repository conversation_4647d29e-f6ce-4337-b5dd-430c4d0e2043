from typing import List, Dict, Any, Optional
from collections import defaultdict
from langchain_openai import ChatOpenAI
from app.config import OPENAI_API_KEY
from app.core.error_handler import AgentError, ErrorCode, validate_input
from app.core.retry_handler import api_retry_decorator
import logging

def _wants_table(prompt: Optional[str]) -> bool:
    if not prompt:
        return False
    p = prompt.lower()
    return any(k in p for k in ["tabel", "table", "markdown", "md"])

def _is_number(x: Any) -> bool:
    return isinstance(x, (int, float))

def _candidate_dims(row: Dict[str, Any]) -> List[str]:
    """
    Pilih kolom dimensi dinamis (string-like) secara heuristik.
    Prioritaskan: product/category/brand/region/channel/month/date
    """
    names = list(row.keys())
    ranked = []
    prio = ["Product_Category", "Product", "Category", "Brand_Name", "Brand",
            "Region", "Channel", "month", "Month", "Date", "date"]
    # yang ada di prioritas:
    for p in prio:
        for k in names:
            if k == p or k.lower() == p.lower():
                ranked.append(k)
    # tambahkan sisanya yang bukan numerik
    for k in names:
        if k not in ranked and not _is_number(row[k]):
            ranked.append(k)
    # ambil sampai 2-3 dimensi
    return ranked[:3]

def _value_col(row: Dict[str, Any]) -> Optional[str]:
    # pilih kolom numerik yang umum dulu
    priority = ["Net_Sales", "net_sales", "Units_Sold", "units_sold", "Profit", "profit", "Revenue", "revenue", "Value", "value"]
    for k in priority:
        if k in row and _is_number(row[k]):
            return k
    # jika tidak ada pilih numerik pertama
    for k, v in row.items():
        if _is_number(v):
            return k
    return None

def _render_markdown_table(rows: List[Dict[str, Any]]) -> str:
    if not rows:
        return "Tidak ada data untuk ditampilkan dalam tabel."

    sample = rows[0]
    dims = _candidate_dims(sample)
    val = _value_col(sample)

    if not val:
        # fallback: render generik 5 kolom pertama
        headers = list(sample.keys())[:5]
        lines = ["| " + " | ".join(headers) + " |", "|" + "|".join(["---"] * len(headers)) + "|"]
        for r in rows:
            lines.append("| " + " | ".join(str(r.get(h, "")) for h in headers) + " |")
        return "\n".join(lines)

    # agregasi deterministik jika ada dims
    if dims:
        agg = defaultdict(float)
        for r in rows:
            key = tuple(str(r.get(d, "")) for d in dims)
            v = r.get(val, 0) or 0
            if _is_number(v):
                agg[key] += float(v)
        # urutkan berdasarkan dims
        items = sorted(agg.items(), key=lambda x: x[0])
        header = dims + [val]
        lines = ["| " + " | ".join(header) + " |", "|" + "|".join(["---"] * len(header)) + "|"]
        for keys, v in items:
            row_cells = list(keys) + [str(int(v) if v.is_integer() else round(v, 2))]
            lines.append("| " + " | ".join(row_cells) + " |")
        return "\n".join(lines)

    # kalau tidak ada dims, tampilkan dua kolom: index & value
    total = sum(float(r.get(val, 0) or 0) for r in rows if _is_number(r.get(val, 0)))
    return f"| Metric | Value |\n|---|---|\n| {val} (total) | {int(total) if total.is_integer() else round(total,2)} |"

logger = logging.getLogger(__name__)

@api_retry_decorator
def summarize_table(table: List[Dict[str, Any]], prompt: Optional[str] = None) -> str:
    """
    Summarize table data with error handling and validation.

    Args:
        table: List of dictionaries representing table data
        prompt: Optional prompt for specific formatting requests

    Returns:
        Summarized text or markdown table

    Raises:
        AgentError: If summarization fails
    """
    try:
        # Validate inputs
        validate_input(
            {"table": table},
            required_fields=["table"],
            field_types={"table": list}
        )

        if _wants_table(prompt):
            return _render_markdown_table(table)

        if not table:
            return "Tidak ada data ditemukan untuk pertanyaan tersebut pada periode default (≤13 bulan). Coba perjelas filter kategori/brand/region atau periode."

        # Validate table structure
        if not all(isinstance(row, dict) for row in table):
            raise AgentError(
                message="Invalid table format: all rows must be dictionaries",
                error_code=ErrorCode.VALIDATION_ERROR,
                details={"table_sample": str(table[:2]) if table else "empty"}
            )

        logger.info(f"Summarizing table with {len(table)} rows")

        try:
            llm = ChatOpenAI(model="gpt-4o-mini", temperature=0.2, api_key=OPENAI_API_KEY)
        except Exception as e:
            raise AgentError(
                message=f"Failed to initialize OpenAI client: {str(e)}",
                error_code=ErrorCode.OPENAI_API_ERROR,
                original_exception=e
            )

        # Limit data size to prevent token overflow
        table_str = str(table)
        if len(table_str) > 10000:  # Limit to ~10k characters
            table_str = str(table[:50]) + f"... (showing first 50 of {len(table)} rows)"
            logger.warning(f"Table data truncated for summarization (original size: {len(table)} rows)")

        result = llm.invoke(
            "Buat ringkasan singkat (≤120 kata) dengan poin utama dan tren per bulan bila ada. "
            "Gunakan bahasa Indonesia yang lugas. Data:\n" + table_str
        )

        summary = (result.content or "").strip()

        if not summary:
            raise AgentError(
                message="OpenAI returned empty summary",
                error_code=ErrorCode.SUMMARIZATION_ERROR,
                details={"table_size": len(table)}
            )

        logger.info("Table summarization completed successfully")
        return summary

    except AgentError:
        # Re-raise AgentErrors as-is
        raise
    except Exception as e:
        # Convert other exceptions to AgentError
        raise AgentError(
            message=f"Table summarization failed: {str(e)}",
            error_code=ErrorCode.SUMMARIZATION_ERROR,
            details={"table_size": len(table) if table else 0, "error_type": type(e).__name__},
            original_exception=e
        )
