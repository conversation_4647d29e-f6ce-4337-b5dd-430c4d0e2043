from clickhouse_connect import get_client
from app.config import (
    CLICKHOUSE_HOST,
    CLICKHOUSE_PORT,
    CLICKHOUSE_USER,
    CLICKHOUSE_PASSWORD,
    CLICKHOUSE_DB,
)
from datetime import date, datetime
from typing import Any, List, Dict
from app.core.error_handler import AgentError, ErrorCode, error_handler
from app.core.retry_handler import database_retry_decorator
import logging

logger = logging.getLogger(__name__)

def _create_client():
    """Create ClickHouse client with error handling."""
    try:
        return get_client(
            host=CLICKHOUSE_HOST,
            port=CLICKHOUSE_PORT,
            username=<PERSON>LICKHOUSE_USER,
            password=CLICKHOUSE_PASSWORD,
            database=CLICKHOUSE_DB,
        )
    except Exception as e:
        raise AgentError(
            message=f"Failed to create ClickHouse client: {str(e)}",
            error_code=ErrorCode.DATABASE_CONNECTION_ERROR,
            original_exception=e
        )

client = _create_client()


def _to_jsonable(value: Any) -> Any:
    if isinstance(value, (date, datetime)):
        return value.isoformat()
    return value


@database_retry_decorator
def run_query(sql: str) -> List[Dict[str, Any]]:
    """
    Execute SQL query with error handling and retry logic.

    Args:
        sql: SQL query string to execute

    Returns:
        List of dictionaries representing query results

    Raises:
        AgentError: If query execution fails
    """
    if not sql or not sql.strip():
        raise AgentError(
            message="SQL query cannot be empty",
            error_code=ErrorCode.VALIDATION_ERROR
        )

    try:
        logger.info(f"Executing ClickHouse query: {sql[:100]}...")
        result = client.query(sql)

        rows: List[Dict[str, Any]] = []
        for row in result.result_rows:
            item = {}
            for col, val in zip(result.column_names, row):
                item[col] = _to_jsonable(val)
            rows.append(item)

        logger.info(f"Query executed successfully, returned {len(rows)} rows")
        return rows

    except Exception as e:
        logger.error(f"ClickHouse query failed: {sql[:100]}... Error: {str(e)}")
        raise AgentError(
            message=f"Database query execution failed: {str(e)}",
            error_code=ErrorCode.DATABASE_QUERY_ERROR,
            details={"sql": sql, "error_type": type(e).__name__},
            original_exception=e
        )
