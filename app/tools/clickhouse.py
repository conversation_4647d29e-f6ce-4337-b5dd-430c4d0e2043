from clickhouse_connect import get_client
from app.config import (
    CLICKHOUSE_HOST,
    CLICKHOUSE_PORT,
    CLICKHOUSE_USER,
    CLICKHOUSE_PASSWORD,
    CLICKHOUSE_DB,
)
from datetime import date, datetime
from typing import Any

client = get_client(
    host=CLICKHOUSE_HOST,
    port=CLICKHOUSE_PORT,
    username=CLICKHOUSE_USER,
    password=CLICKHOUSE_PASSWORD,
    database=CLICKHOUSE_DB,
)


def _to_jsonable(value: Any) -> Any:
    if isinstance(value, (date, datetime)):
        return value.isoformat()
    return value


def run_query(sql: str) -> list[dict]:
    result = client.query(sql)
    rows: list[dict] = []
    for row in result.result_rows:
        item = {}
        for col, val in zip(result.column_names, row):
            item[col] = _to_jsonable(val)
        rows.append(item)
    return rows
