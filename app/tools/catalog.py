from typing import Dict, List
from app.tools.clickhouse import run_query
from app.config import CLICKHOUSE_DB

VIEW_ENGINES = {"View", "MaterializedView", "LiveView", "WindowView"}


def fetch_catalog() -> Dict:
    # daftar tabel & engine
    tables = run_query(
        f"""
        SELECT name, engine
        FROM system.tables
        WHERE database = '{CLICKHOUSE_DB}'
        ORDER BY name
        """
    )
    # daftar kolom per tabel
    cols = run_query(
        f"""
        SELECT
          table AS table_name,
          name  AS column_name,
          type  AS column_type
        FROM system.columns
        WHERE database = '{CLICKHOUSE_DB}'
        ORDER BY table, position
        """
    )
    columns_map: Dict[str, List[Dict]] = {}
    for c in cols:
        t = c["table_name"]
        columns_map.setdefault(t, []).append(
            {
                "name": c["column_name"],
                "type": c["column_type"],
            }
        )

    views, plain_tables = [], []
    for t in tables:
        (views if t["engine"] in VIEW_ENGINES else plain_tables).append(t["name"])

    return {
        "database": CLICKHOUSE_DB,
        "tables": plain_tables,
        "views": views,
        "columns": columns_map,  # dict: table -> [{name,type}]
    }


def catalog_as_prompt_text(catalog: Dict) -> str:
    """
    Ubah katalog ke teks ringkas untuk konteks LLM.
    Format:
    - table_name: col1, col2, ...
    """
    lines = []
    # tampilkan tables dulu, lalu views
    for t in catalog.get("tables", []):
        cols = ", ".join(col["name"] for col in catalog["columns"].get(t, [])[:20])
        lines.append(f"- {t}: {cols}")
    for v in catalog.get("views", []):
        cols = ", ".join(col["name"] for col in catalog["columns"].get(v, [])[:20])
        lines.append(f"- {v} (VIEW): {cols}")
    return "\n".join(lines) if lines else "(no tables/views)"
