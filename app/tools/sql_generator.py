from typing import Final, Dict
import re
from langchain_openai import ChatOpenAI
from app.config import OPENAI_API_KEY
from app.tools.catalog import catalog_as_prompt_text

_MODEL: Final[str] = "gpt-4o-mini"


def _sanitize_sql(text: str) -> str:
    if not text:
        return text
    text = (
        text.replace("```sql", "```")
        .replace("```SQL", "```")
        .replace("```", "\n")
        .strip()
    )
    m = re.search(r"\b(SELECT|WITH|INSERT|UPDATE|DELETE)\b", text, flags=re.IGNORECASE)
    if m:
        text = text[m.start() :].strip()
    sql = re.split(r"\n\s*\n", text.strip(), maxsplit=1)[0].strip()
    if sql.endswith(";"):
        sql = sql[:-1].strip()
    return sql


def generate_sql(prompt: str, catalog: Dict) -> str:
    print(f"🧠 [SQL GEN] Prompt: {prompt}")
    llm = ChatOpenAI(model=_MODEL, temperature=0, api_key=OPENAI_API_KEY)

    schema_text = catalog_as_prompt_text(catalog)

    system_rules = f"""
Kamu adalah asisten data analyst untuk ClickHouse.
KELUARKAN HANYA 1 STATEMENT SQL (tanpa penjelasan/markdown).
Gunakan HANYA tabel/view yang tersedia berikut (jangan membuat nama tabel baru):

{schema_text}

Aturan:
- Default periode: 13 bulan terakhir jika user tidak menyebutkan periode (gunakan: toStartOfMonth(Date) >= addMonths(toStartOfMonth(today()), -12))
- Gunakan kolom yang memang ada pada tabel tersebut.
- Jika perlu agregasi bulanan: gunakan toStartOfMonth(Date) AS month
- Jangan gunakan alias tabel/kolom yang tidak ada.
- Output: hanya SQL, satu baris/statement.
"""

    response = llm.invoke(system_rules + "\nPertanyaan pengguna:\n" + prompt + "\nSQL:")
    raw = (response.content or "").strip()
    sql = _sanitize_sql(raw)
    print(f"📄 [SQL GEN] SQL (sanitized): {sql}")
    return sql
