from typing import Final, Dict
import re
from langchain_openai import ChatOpenAI
from app.config import OPENAI_API_KEY
from app.tools.catalog import catalog_as_prompt_text
from app.core.error_handler import AgentError, ErrorCode, validate_input
from app.core.retry_handler import api_retry_decorator
import logging

_MODEL: Final[str] = "gpt-4o-mini"
logger = logging.getLogger(__name__)


def _sanitize_sql(text: str) -> str:
    if not text:
        return text
    text = (
        text.replace("```sql", "```")
        .replace("```SQL", "```")
        .replace("```", "\n")
        .strip()
    )
    m = re.search(r"\b(SELECT|WITH|INSERT|UPDATE|DELETE)\b", text, flags=re.IGNORECASE)
    if m:
        text = text[m.start() :].strip()
    sql = re.split(r"\n\s*\n", text.strip(), maxsplit=1)[0].strip()
    if sql.endswith(";"):
        sql = sql[:-1].strip()
    return sql


@api_retry_decorator
def generate_sql(prompt: str, catalog: Dict) -> str:
    """
    Generate SQL query from natural language prompt with error handling.

    Args:
        prompt: Natural language query description
        catalog: Database catalog information

    Returns:
        Generated SQL query string

    Raises:
        AgentError: If SQL generation fails
    """
    # Validate inputs
    validate_input(
        {"prompt": prompt, "catalog": catalog},
        required_fields=["prompt", "catalog"],
        field_types={"prompt": str, "catalog": dict}
    )

    if not prompt.strip():
        raise AgentError(
            message="Prompt cannot be empty",
            error_code=ErrorCode.INVALID_INPUT
        )

    try:
        logger.info(f"🧠 [SQL GEN] Prompt: {prompt}")

        # Create LLM instance with error handling
        try:
            llm = ChatOpenAI(model=_MODEL, temperature=0, api_key=OPENAI_API_KEY)
        except Exception as e:
            raise AgentError(
                message=f"Failed to initialize OpenAI client: {str(e)}",
                error_code=ErrorCode.OPENAI_API_ERROR,
                original_exception=e
            )

        schema_text = catalog_as_prompt_text(catalog)

        if not schema_text or schema_text == "(no tables/views)":
            raise AgentError(
                message="No database schema available for SQL generation",
                error_code=ErrorCode.SQL_GENERATION_ERROR,
                details={"catalog": catalog}
            )

        system_rules = f"""
Kamu adalah asisten data analyst untuk ClickHouse.
KELUARKAN HANYA 1 STATEMENT SQL (tanpa penjelasan/markdown).
Gunakan HANYA tabel/view yang tersedia berikut (jangan membuat nama tabel baru):

{schema_text}

Aturan:
- Default periode: 13 bulan terakhir jika user tidak menyebutkan periode (gunakan: toStartOfMonth(Date) >= addMonths(toStartOfMonth(today()), -12))
- Gunakan kolom yang memang ada pada tabel tersebut.
- Jika perlu agregasi bulanan: gunakan toStartOfMonth(Date) AS month
- Jangan gunakan alias tabel/kolom yang tidak ada.
- Output: hanya SQL, satu baris/statement.
"""

        response = llm.invoke(system_rules + "\nPertanyaan pengguna:\n" + prompt + "\nSQL:")
        raw = (response.content or "").strip()

        if not raw:
            raise AgentError(
                message="OpenAI returned empty response",
                error_code=ErrorCode.SQL_GENERATION_ERROR,
                details={"prompt": prompt}
            )

        sql = _sanitize_sql(raw)

        if not sql:
            raise AgentError(
                message="Failed to extract valid SQL from OpenAI response",
                error_code=ErrorCode.SQL_GENERATION_ERROR,
                details={"raw_response": raw, "prompt": prompt}
            )

        logger.info(f"📄 [SQL GEN] SQL (sanitized): {sql}")
        return sql

    except AgentError:
        # Re-raise AgentErrors as-is
        raise
    except Exception as e:
        # Convert other exceptions to AgentError
        raise AgentError(
            message=f"SQL generation failed: {str(e)}",
            error_code=ErrorCode.SQL_GENERATION_ERROR,
            details={"prompt": prompt, "error_type": type(e).__name__},
            original_exception=e
        )
