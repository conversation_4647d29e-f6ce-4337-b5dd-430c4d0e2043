import redis
import json
from datetime import datetime, date
from typing import Any, Dict, Optional
from app.config import REDISLOGS_HOST, REDISLOGS_PORT, REDISLOGS_PASSWORD
from app.core.error_handler import AgentError, ErrorCode, error_handler
from app.core.retry_handler import redis_retry_decorator
import logging


def _json_default(obj: Any):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    return obj  # biarkan json lib yang error jika tidak serializable


logger = logging.getLogger(__name__)

def _connect() -> Optional[redis.Redis]:
    """Create Redis connection with error handling."""
    try:
        return redis.Redis(
            host=REDISLOGS_HOST,
            port=int(REDISLOGS_PORT),
            password=REDISLOGS_PASSWORD,
            decode_responses=True,
            socket_connect_timeout=2,
        )
    except Exception as e:
        logger.warning(f"Failed to create Redis connection: {e}")
        return None


try:
    r = _connect()
    if r:
        r.ping()
        logger.info("✅ Redis logs connected")
    else:
        logger.warning("⚠️ Redis logs disabled (connection failed)")
except Exception as e:
    r = None
    logger.warning(f"⚠️ Redis logs disabled (reason: {e})")


@redis_retry_decorator
def log_session(payload_json: Dict[str, Any]) -> bool:
    """
    Save memory/log as JSON with error handling and graceful degradation.

    Args:
        payload_json: Data to log

    Returns:
        True if logging succeeded, False otherwise
    """
    try:
        ts = datetime.utcnow().isoformat()
        payload_json["logged_at"] = ts
        key_log = f"fin_fmcg:log:{ts}"

        if r is not None:
            # Use Redis for persistent logging
            serialized_data = json.dumps(payload_json, default=_json_default)
            r.set(key_log, serialized_data)

            # Save session pointer if session_id exists
            session_id = (
                ((payload_json.get("session") or {}).get("session_id"))
                if isinstance(payload_json.get("session"), dict)
                else None
            )
            if session_id:
                r.set(f"fin_fmcg:session:{session_id}:last", serialized_data)

            logger.debug(f"Successfully logged to Redis: {key_log}")
            return True
        else:
            # Graceful degradation: log to console if Redis unavailable
            logger.info(f"🗒️ LOG (no-redis): {json.dumps(payload_json, default=_json_default)[:800]}...")
            return False

    except Exception as e:
        # Graceful degradation: don't fail the main operation if logging fails
        logger.warning(f"Redis log error (graceful degradation): {e}")
        try:
            # Fallback to console logging
            logger.info(f"🗒️ LOG (fallback): {json.dumps(payload_json, default=_json_default)[:800]}...")
        except Exception as fallback_error:
            logger.error(f"Complete logging failure: {fallback_error}")
        return False
