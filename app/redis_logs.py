import redis
import json
from datetime import datetime, date
from typing import Any, Dict
from app.config import REDISLOGS_HOST, REDISLOGS_PORT, REDISLOGS_PASSWORD


def _json_default(obj: Any):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    return obj  # biarkan json lib yang error jika tidak serializable


def _connect():
    return redis.Redis(
        host=REDISLOGS_HOST,
        port=int(REDISLOGS_PORT),
        password=REDISLOGS_PASSWORD,
        decode_responses=True,
        socket_connect_timeout=2,
    )


try:
    r = _connect()
    r.ping()
    print("✅ Redis logs connected")
except Exception as e:
    r = None
    print(f"⚠️ Redis logs disabled (reason: {e})")


def log_session(payload_json: Dict[str, Any]):
    """
    Simpan MEMORI/LOG sebagai JSON murni.
    Struktur bebas (dict), akan diserialisasi sekali.
    Key-level:
      - fin_fmcg:session:{session_id}:last
      - fin_fmcg:log:{timestamp}
    """
    try:
        ts = datetime.utcnow().isoformat()
        payload_json["logged_at"] = ts
        key_log = f"fin_fmcg:log:{ts}"

        if r is not None:
            r.set(key_log, json.dumps(payload_json, default=_json_default))
            # jika ada session_id, simpan juga pointer 'last'
            session_id = (
                ((payload_json.get("session") or {}).get("session_id"))
                if isinstance(payload_json.get("session"), dict)
                else None
            )
            if session_id:
                r.set(
                    f"fin_fmcg:session:{session_id}:last",
                    json.dumps(payload_json, default=_json_default),
                )
        else:
            print(
                f"🗒️ LOG (no-redis): {json.dumps(payload_json, default=_json_default)[:800]}..."
            )
    except Exception as e:
        print(f"⚠️ Redis log error: {e}")
