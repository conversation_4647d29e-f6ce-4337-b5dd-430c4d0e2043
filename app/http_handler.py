from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from datetime import date, datetime
from typing import Any
from app.agent.graph import build_graph

router = APIRouter()
graph = build_graph()


def _json_sanitize(obj: Any):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if isinstance(obj, list):
        return [_json_sanitize(x) for x in obj]
    if isinstance(obj, dict):
        return {k: _json_sanitize(v) for k, v in obj.items()}
    return obj


@router.post("/chat")
async def chat_endpoint(request: Request):
    try:
        data = await request.json()
        prompt = data.get("message")
        if not prompt:
            return JSONResponse(
                {"error": "Field 'message' is required."}, status_code=400
            )

        # meta sesi (opsional)
        user_id = data.get("user_id")
        session_id = data.get("session_id")
        timestamp = data.get("timestamp")

        print(f"📥 Prompt received: {prompt}")

        state = {
            "input": prompt,
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
        }

        result = {}
        for step in graph.stream(state):
            # debug streaming langkah
            print(f"🔁 Step: {step}")
            result.update(step)

        sanitized = _json_sanitize(result)
        return JSONResponse(sanitized)

    except Exception as e:
        print(f"💥 Exception in /chat handler: {e}")
        return JSONResponse({"error": str(e)}, status_code=500)
