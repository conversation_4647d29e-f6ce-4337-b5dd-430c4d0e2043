from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from datetime import date, datetime
from typing import Any
from app.agent.graph import build_graph
from app.core.error_handler import AgentError, ErrorCode, error_handler, validate_input
import logging

router = APIRouter()
graph = build_graph()
logger = logging.getLogger(__name__)


def _json_sanitize(obj: Any):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    if isinstance(obj, list):
        return [_json_sanitize(x) for x in obj]
    if isinstance(obj, dict):
        return {k: _json_sanitize(v) for k, v in obj.items()}
    return obj


@router.post("/chat")
async def chat_endpoint(request: Request):
    """Enhanced chat endpoint with comprehensive error handling and validation."""
    try:
        # Parse and validate request
        try:
            data = await request.json()
        except Exception as e:
            raise AgentError(
                message="Invalid JSON in request body",
                error_code=ErrorCode.VALIDATION_ERROR,
                original_exception=e
            )

        # Validate required fields
        validate_input(
            data,
            required_fields=["message"],
            field_types={"message": str}
        )

        prompt = data.get("message", "").strip()
        if not prompt:
            raise AgentError(
                message="Message cannot be empty",
                error_code=ErrorCode.INVALID_INPUT
            )

        # Extract session metadata
        user_id = data.get("user_id")
        session_id = data.get("session_id")
        timestamp = data.get("timestamp")

        logger.info(f"📥 Chat request - User: {user_id}, Session: {session_id}, Prompt: {prompt[:100]}...")

        # Prepare agent state
        state = {
            "input": prompt,
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": timestamp,
        }

        # Execute agent graph with error handling
        result = {}
        try:
            for step in graph.stream(state):
                logger.debug(f"🔁 Graph step: {list(step.keys())}")
                result.update(step)
        except AgentError as agent_error:
            # Handle agent-specific errors
            logger.error(f"Agent error during execution: {agent_error}")
            error_response = error_handler.handle_error(agent_error)
            return JSONResponse(error_response, status_code=400)
        except Exception as graph_error:
            # Handle unexpected graph errors
            logger.error(f"Unexpected graph error: {graph_error}")
            error_response = error_handler.handle_error(
                graph_error,
                context={"endpoint": "chat", "user_id": user_id, "session_id": session_id}
            )
            return JSONResponse(error_response, status_code=500)

        # Sanitize and return successful response
        sanitized = _json_sanitize(result)

        # Create standardized success response
        success_response = error_handler.create_success_response(
            data=sanitized,
            metadata={
                "user_id": user_id,
                "session_id": session_id,
                "processing_steps": len([k for k in result.keys() if not k.startswith("_")])
            }
        )

        logger.info(f"✅ Chat request completed successfully for session {session_id}")
        return JSONResponse(success_response)

    except AgentError as agent_error:
        # Handle validation and known errors
        logger.warning(f"Chat endpoint validation error: {agent_error}")
        error_response = error_handler.handle_error(agent_error)
        return JSONResponse(error_response, status_code=400)

    except Exception as e:
        # Handle unexpected errors
        logger.error(f"💥 Unexpected error in chat endpoint: {e}")
        error_response = error_handler.handle_error(
            e,
            context={"endpoint": "chat", "request_data": str(data) if 'data' in locals() else "unavailable"}
        )
        return JSONResponse(error_response, status_code=500)
