"""Memory management system for the AI agent."""

import json
import redis
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from app.config import REDISLOGS_HOST, REDISLOGS_PORT, REDISLOGS_PASSWORD
from app.core.error_handler import <PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>, error_handler
from app.core.retry_handler import redis_retry_decorator
import logging
import hashlib


@dataclass
class ConversationTurn:
    turn_id: str
    user_input: str
    agent_response: str
    intent: str
    sql_query: Optional[str] = None
    data_summary: Optional[str] = None
    timestamp: str = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class UserPreferences:
    user_id: str
    preferred_language: str = "id"
    preferred_format: str = "summary"
    common_queries: List[str] = None
    interaction_patterns: Dict[str, Any] = None
    last_updated: str = None

    def __post_init__(self):
        if self.common_queries is None:
            self.common_queries = []
        if self.interaction_patterns is None:
            self.interaction_patterns = {}
        if self.last_updated is None:
            self.last_updated = datetime.utcnow().isoformat()


@dataclass
class ConversationContext:
    session_id: str
    user_id: str
    turns: List[ConversationTurn]
    current_topic: Optional[str] = None
    context_summary: Optional[str] = None
    created_at: str = None
    last_activity: str = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow().isoformat()
        if self.last_activity is None:
            self.last_activity = datetime.utcnow().isoformat()


class MemoryManager:
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.logger = logging.getLogger(__name__)
        self.redis_client = redis_client or self._create_redis_client()
        self.memory_enabled = self.redis_client is not None

        self.max_conversation_turns = 50
        self.context_retention_days = 30
        self.max_context_length = 5000

        if self.memory_enabled:
            self.logger.info("Memory manager initialized with Redis backend")
        else:
            self.logger.warning("Memory manager initialized without Redis")
    
    def _create_redis_client(self) -> Optional[redis.Redis]:
        try:
            client = redis.Redis(
                host=REDISLOGS_HOST,
                port=int(REDISLOGS_PORT),
                password=REDISLOGS_PASSWORD,
                decode_responses=True,
                socket_connect_timeout=2,
            )
            client.ping()
            return client
        except Exception as e:
            self.logger.warning(f"Failed to create Redis client for memory: {e}")
            return None

    def _generate_turn_id(self, session_id: str, user_input: str) -> str:
        content = f"{session_id}:{user_input}:{datetime.utcnow().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    @redis_retry_decorator
    def store_conversation_turn(
        self,
        session_id: str,
        user_id: str,
        user_input: str,
        agent_response: str,
        intent: str,
        sql_query: Optional[str] = None,
        data_summary: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        if not self.memory_enabled:
            return "memory_disabled"
        
        try:
            turn_id = self._generate_turn_id(session_id, user_input)

            turn = ConversationTurn(
                turn_id=turn_id,
                user_input=user_input,
                agent_response=agent_response,
                intent=intent,
                sql_query=sql_query,
                data_summary=data_summary,
                metadata=metadata or {}
            )

            turn_key = f"fin_fmcg:memory:turn:{turn_id}"
            self.redis_client.setex(
                turn_key,
                timedelta(days=self.context_retention_days),
                json.dumps(asdict(turn))
            )

            session_key = f"fin_fmcg:memory:session:{session_id}"
            self.redis_client.lpush(session_key, turn_id)
            self.redis_client.expire(session_key, timedelta(days=self.context_retention_days))
            self.redis_client.ltrim(session_key, 0, self.max_conversation_turns - 1)

            self._update_session_metadata(session_id, user_id)
            return turn_id

        except Exception as e:
            self.logger.error(f"Failed to store conversation turn: {e}")
            raise AgentError(
                message=f"Memory storage failed: {str(e)}",
                error_code=ErrorCode.MEMORY_ERROR,
                original_exception=e
            )
    
    @redis_retry_decorator
    def get_conversation_history(
        self,
        session_id: str,
        limit: int = 10
    ) -> List[ConversationTurn]:
        if not self.memory_enabled:
            return []
        
        try:
            session_key = f"fin_fmcg:memory:session:{session_id}"
            turn_ids = self.redis_client.lrange(session_key, 0, limit - 1)

            turns = []
            for turn_id in reversed(turn_ids):
                turn_key = f"fin_fmcg:memory:turn:{turn_id}"
                turn_data = self.redis_client.get(turn_key)

                if turn_data:
                    try:
                        turn_dict = json.loads(turn_data)
                        turns.append(ConversationTurn(**turn_dict))
                    except (json.JSONDecodeError, TypeError) as e:
                        self.logger.warning(f"Failed to parse turn data for {turn_id}: {e}")
                        continue

            return turns

        except Exception as e:
            self.logger.error(f"Failed to retrieve conversation history: {e}")
            return []
    
    def _update_session_metadata(self, session_id: str, user_id: str):
        try:
            metadata_key = f"fin_fmcg:memory:session_meta:{session_id}"
            metadata = {
                "user_id": user_id,
                "last_activity": datetime.utcnow().isoformat(),
                "session_id": session_id
            }
            self.redis_client.setex(
                metadata_key,
                timedelta(days=self.context_retention_days),
                json.dumps(metadata)
            )
        except Exception as e:
            self.logger.warning(f"Failed to update session metadata: {e}")
    
    @redis_retry_decorator
    def store_user_preferences(self, preferences: UserPreferences) -> bool:
        if not self.memory_enabled:
            return False

        try:
            preferences.last_updated = datetime.utcnow().isoformat()
            prefs_key = f"fin_fmcg:memory:user_prefs:{preferences.user_id}"

            self.redis_client.setex(
                prefs_key,
                timedelta(days=90),
                json.dumps(asdict(preferences))
            )

            return True

        except Exception as e:
            self.logger.error(f"Failed to store user preferences: {e}")
            return False
    
    @redis_retry_decorator
    def get_user_preferences(self, user_id: str) -> Optional[UserPreferences]:
        if not self.memory_enabled:
            return None

        try:
            prefs_key = f"fin_fmcg:memory:user_prefs:{user_id}"
            prefs_data = self.redis_client.get(prefs_key)

            if prefs_data:
                prefs_dict = json.loads(prefs_data)
                return UserPreferences(**prefs_dict)

            return None

        except Exception as e:
            self.logger.error(f"Failed to retrieve user preferences: {e}")
            return None
    
    def generate_context_summary(self, turns: List[ConversationTurn]) -> str:
        if not turns:
            return ""

        recent_topics = []
        recent_queries = []

        for turn in turns[-5:]:
            if turn.intent:
                recent_topics.append(turn.intent)
            if turn.sql_query:
                recent_queries.append(turn.sql_query)

        summary_parts = []

        if recent_topics:
            unique_topics = list(set(recent_topics))
            summary_parts.append(f"Recent topics: {', '.join(unique_topics)}")

        if recent_queries:
            summary_parts.append(f"Recent queries: {len(recent_queries)} database queries executed")

        summary = "; ".join(summary_parts)

        if len(summary) > self.max_context_length:
            summary = summary[:self.max_context_length] + "..."

        return summary
    
    def get_relevant_context(
        self,
        session_id: str,
        current_input: str,
        max_turns: int = 5
    ) -> Tuple[List[ConversationTurn], str]:
        history = self.get_conversation_history(session_id, limit=max_turns)
        context_summary = self.generate_context_summary(history)
        return history, context_summary

    def cleanup_old_memories(self, days_old: int = 30) -> int:
        if not self.memory_enabled:
            return 0
        return 0


memory_manager = MemoryManager()
