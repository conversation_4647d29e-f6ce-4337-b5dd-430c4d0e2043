"""Error handling framework for the AI agent system."""

import logging
import traceback
from enum import Enum
from typing import Dict, Any, Optional, Union
from datetime import datetime
import json


class ErrorCode(Enum):
    """Standardized error codes for the agent system."""
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    DATABASE_CONNECTION_ERROR = "DATABASE_CONNECTION_ERROR"
    DATABASE_QUERY_ERROR = "DATABASE_QUERY_ERROR"
    REDIS_CONNECTION_ERROR = "REDIS_CONNECTION_ERROR"
    OPENAI_API_ERROR = "OPENAI_API_ERROR"
    EXTERNAL_SERVICE_TIMEOUT = "EXTERNAL_SERVICE_TIMEOUT"
    INTENT_DETECTION_ERROR = "INTENT_DETECTION_ERROR"
    ROUTING_ERROR = "ROUTING_ERROR"
    MEMORY_ERROR = "MEMORY_ERROR"
    SQL_GENERATION_ERROR = "SQL_GENERATION_ERROR"
    SUMMARIZATION_ERROR = "SUMMARIZATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    OUTPUT_SERIALIZATION_ERROR = "OUTPUT_SERIALIZATION_ERROR"


class AgentError(Exception):
    """Base exception class for agent-specific errors."""

    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_exception = original_exception
        self.user_message = user_message or self._generate_user_message()
        self.timestamp = datetime.utcnow()

    def _generate_user_message(self) -> str:
        user_messages = {
            ErrorCode.DATABASE_CONNECTION_ERROR: "Maaf, terjadi masalah koneksi database. Silakan coba lagi dalam beberapa saat.",
            ErrorCode.DATABASE_QUERY_ERROR: "Maaf, terjadi kesalahan saat mengambil data. Silakan periksa permintaan Anda.",
            ErrorCode.OPENAI_API_ERROR: "Maaf, layanan AI sedang mengalami gangguan. Silakan coba lagi nanti.",
            ErrorCode.INVALID_INPUT: "Maaf, input yang diberikan tidak valid. Silakan periksa format input Anda.",
            ErrorCode.MISSING_REQUIRED_FIELD: "Maaf, ada field yang wajib diisi yang belum lengkap.",
            ErrorCode.INTENT_DETECTION_ERROR: "Maaf, saya tidak dapat memahami maksud permintaan Anda. Bisakah Anda menjelaskan lebih detail?",
            ErrorCode.SQL_GENERATION_ERROR: "Maaf, terjadi kesalahan saat memproses query data. Silakan coba dengan permintaan yang lebih spesifik.",
            ErrorCode.SUMMARIZATION_ERROR: "Maaf, terjadi kesalahan saat membuat ringkasan. Data mungkin terlalu kompleks.",
            ErrorCode.VALIDATION_ERROR: "Maaf, terjadi kesalahan validasi input. Silakan periksa data yang diberikan.",
        }
        return user_messages.get(self.error_code, "Maaf, terjadi kesalahan sistem. Silakan coba lagi nanti.")
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "error_code": self.error_code.value,
            "message": self.message,
            "user_message": self.user_message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class ErrorHandler:
    """Centralized error handler for the agent system."""

    def __init__(self, logger_name: str = "agent_error_handler"):
        self.logger = logging.getLogger(logger_name)
        self._setup_logging()

    def _setup_logging(self):
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def handle_error(
        self,
        error: Union[Exception, AgentError],
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = False
    ) -> Dict[str, Any]:
        context = context or {}

        if isinstance(error, AgentError):
            agent_error = error
        else:
            agent_error = self._convert_to_agent_error(error, context)

        self._log_error(agent_error, context)

        response = {
            "success": False,
            "error": agent_error.to_dict(),
            "context": context
        }

        if reraise:
            raise agent_error

        return response
    
    def _convert_to_agent_error(self, error: Exception, context: Dict[str, Any]) -> AgentError:
        error_type = type(error).__name__
        error_message = str(error)

        error_code_mapping = {
            "ConnectionError": ErrorCode.DATABASE_CONNECTION_ERROR,
            "TimeoutError": ErrorCode.EXTERNAL_SERVICE_TIMEOUT,
            "ValueError": ErrorCode.VALIDATION_ERROR,
            "KeyError": ErrorCode.MISSING_REQUIRED_FIELD,
            "TypeError": ErrorCode.VALIDATION_ERROR,
            "JSONDecodeError": ErrorCode.OUTPUT_SERIALIZATION_ERROR,
        }

        error_code = error_code_mapping.get(error_type, ErrorCode.UNKNOWN_ERROR)

        return AgentError(
            message=f"{error_type}: {error_message}",
            error_code=error_code,
            details={"exception_type": error_type, "traceback": traceback.format_exc()},
            original_exception=error
        )
    
    def _log_error(self, error: AgentError, context: Dict[str, Any]):
        log_data = {
            "error_code": error.error_code.value,
            "message": error.message,
            "context": context
        }

        if error.error_code in [ErrorCode.UNKNOWN_ERROR, ErrorCode.DATABASE_CONNECTION_ERROR]:
            self.logger.error(f"Critical error: {json.dumps(log_data)}")
        elif error.error_code in [ErrorCode.VALIDATION_ERROR, ErrorCode.INVALID_INPUT]:
            self.logger.warning(f"Validation error: {json.dumps(log_data)}")
        else:
            self.logger.info(f"Handled error: {json.dumps(log_data)}")
    
    def create_success_response(self, data: Any, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        return {
            "success": True,
            "data": data,
            "metadata": metadata or {},
            "timestamp": datetime.utcnow().isoformat()
        }


error_handler = ErrorHandler()


def handle_exceptions(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return error_handler.handle_error(e, context={"function": func.__name__})
    return wrapper


def validate_input(data: Dict[str, Any], required_fields: list, field_types: Optional[Dict[str, type]] = None) -> None:
    field_types = field_types or {}

    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise AgentError(
            message=f"Missing required fields: {missing_fields}",
            error_code=ErrorCode.MISSING_REQUIRED_FIELD,
            details={"missing_fields": missing_fields}
        )

    type_errors = []
    for field, expected_type in field_types.items():
        if field in data and not isinstance(data[field], expected_type):
            type_errors.append({
                "field": field,
                "expected_type": expected_type.__name__,
                "actual_type": type(data[field]).__name__
            })

    if type_errors:
        raise AgentError(
            message=f"Type validation failed for fields: {[e['field'] for e in type_errors]}",
            error_code=ErrorCode.VALIDATION_ERROR,
            details={"type_errors": type_errors}
        )
