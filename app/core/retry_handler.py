"""
Retry mechanism with exponential backoff for external service calls.
Provides configurable retry logic for database, Redis, and API calls.
"""

import time
import random
import logging
from typing import Callable, Any, Optional, Dict, List, Type
from functools import wraps
from app.core.error_handler import AgentError, ErrorCode


class RetryConfig:
    """Configuration for retry behavior."""
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
        retryable_error_codes: Optional[List[ErrorCode]] = None
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.retryable_exceptions = retryable_exceptions or [
            ConnectionError,
            TimeoutError,
            OSError,
        ]
        self.retryable_error_codes = retryable_error_codes or [
            ErrorCode.DATABASE_CONNECTION_ERROR,
            ErrorCode.REDIS_CONNECTION_ERROR,
            ErrorCode.EXTERNAL_SERVICE_TIMEOUT,
            ErrorCode.OPENAI_API_ERROR,
        ]


class RetryHandler:
    """Handles retry logic with exponential backoff."""
    
    def __init__(self, config: Optional[RetryConfig] = None):
        self.config = config or RetryConfig()
        self.logger = logging.getLogger("retry_handler")
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """Determine if an exception should trigger a retry."""
        if attempt >= self.config.max_attempts:
            return False
        
        # Check if it's an AgentError with retryable error code
        if isinstance(exception, AgentError):
            return exception.error_code in self.config.retryable_error_codes
        
        # Check if it's a retryable exception type
        return any(isinstance(exception, exc_type) for exc_type in self.config.retryable_exceptions)
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for the given attempt number."""
        delay = self.config.base_delay * (self.config.exponential_base ** (attempt - 1))
        delay = min(delay, self.config.max_delay)
        
        if self.config.jitter:
            # Add random jitter to prevent thundering herd
            jitter_range = delay * 0.1
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def execute_with_retry(
        self,
        func: Callable,
        *args,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Any:
        """
        Execute a function with retry logic.
        
        Args:
            func: Function to execute
            *args: Positional arguments for the function
            context: Additional context for logging
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result of the function execution
            
        Raises:
            The last exception if all retry attempts fail
        """
        context = context or {}
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                self.logger.debug(f"Executing {func.__name__}, attempt {attempt}/{self.config.max_attempts}")
                result = func(*args, **kwargs)
                
                if attempt > 1:
                    self.logger.info(f"Function {func.__name__} succeeded on attempt {attempt}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                if not self.should_retry(e, attempt):
                    self.logger.warning(f"Function {func.__name__} failed on attempt {attempt}, not retrying: {e}")
                    raise e
                
                if attempt < self.config.max_attempts:
                    delay = self.calculate_delay(attempt)
                    self.logger.warning(
                        f"Function {func.__name__} failed on attempt {attempt}/{self.config.max_attempts}, "
                        f"retrying in {delay:.2f}s: {e}"
                    )
                    time.sleep(delay)
                else:
                    self.logger.error(f"Function {func.__name__} failed on final attempt {attempt}: {e}")
        
        # If we get here, all attempts failed
        raise last_exception


# Default retry configurations for different service types
DATABASE_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=10.0,
    retryable_exceptions=[ConnectionError, TimeoutError, OSError],
    retryable_error_codes=[ErrorCode.DATABASE_CONNECTION_ERROR, ErrorCode.DATABASE_QUERY_ERROR]
)

REDIS_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    base_delay=0.5,
    max_delay=5.0,
    retryable_exceptions=[ConnectionError, TimeoutError],
    retryable_error_codes=[ErrorCode.REDIS_CONNECTION_ERROR]
)

API_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=2.0,
    max_delay=30.0,
    retryable_exceptions=[ConnectionError, TimeoutError],
    retryable_error_codes=[ErrorCode.OPENAI_API_ERROR, ErrorCode.EXTERNAL_SERVICE_TIMEOUT]
)

# Global retry handlers
database_retry = RetryHandler(DATABASE_RETRY_CONFIG)
redis_retry = RetryHandler(REDIS_RETRY_CONFIG)
api_retry = RetryHandler(API_RETRY_CONFIG)


def retry_on_failure(config: Optional[RetryConfig] = None):
    """
    Decorator for automatic retry on function failure.
    
    Args:
        config: Retry configuration to use
        
    Returns:
        Decorated function with retry logic
    """
    retry_handler = RetryHandler(config or RetryConfig())
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return retry_handler.execute_with_retry(
                func, *args, context={"function": func.__name__}, **kwargs
            )
        return wrapper
    return decorator


def database_retry_decorator(func: Callable) -> Callable:
    """Decorator for database operations with appropriate retry config."""
    return retry_on_failure(DATABASE_RETRY_CONFIG)(func)


def redis_retry_decorator(func: Callable) -> Callable:
    """Decorator for Redis operations with appropriate retry config."""
    return retry_on_failure(REDIS_RETRY_CONFIG)(func)


def api_retry_decorator(func: Callable) -> Callable:
    """Decorator for API operations with appropriate retry config."""
    return retry_on_failure(API_RETRY_CONFIG)(func)
