"""
Advanced routing system for the AI agent.
Provides sophisticated intent classification, dynamic routing, and fallback mechanisms.
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from app.core.error_handler import Agent<PERSON>rror, ErrorCode
from app.core.memory_manager import MemoryManager, ConversationTurn
import logging


class IntentType(Enum):
    """Supported intent types."""
    QUERY = "query"
    SUMMARY = "summary"
    GREETING = "greeting"
    HELP = "help"
    CLARIFICATION = "clarification"
    FOLLOW_UP = "follow_up"
    UNKNOWN = "unknown"


@dataclass
class IntentClassification:
    """Result of intent classification."""
    intent: IntentType
    confidence: float
    reasoning: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class RoutingRule:
    """Routing rule configuration."""
    intent: IntentType
    target_node: str
    conditions: Dict[str, Any] = None
    priority: int = 0
    enabled: bool = True
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = {}


class AdvancedRouter:
    """
    Advanced routing system with intent classification and dynamic routing.
    """
    
    def __init__(self, memory_manager: Optional[MemoryManager] = None):
        self.logger = logging.getLogger(__name__)
        self.memory_manager = memory_manager
        
        # Intent classification patterns
        self.intent_patterns = self._initialize_intent_patterns()
        
        # Routing rules
        self.routing_rules = self._initialize_routing_rules()
        
        # Fallback configuration
        self.default_route = "query"
        self.confidence_threshold = 0.6
        
        self.logger.info("Advanced router initialized")
    
    def _initialize_intent_patterns(self) -> Dict[IntentType, List[Dict[str, Any]]]:
        """Initialize intent classification patterns."""
        return {
            IntentType.QUERY: [
                {
                    "patterns": [
                        r"\b(tampilkan|data|grafik|penjualan|sales|berdasarkan|per bulan|trend|tren)\b",
                        r"\b(berapa|jumlah|total|rata-rata|maksimum|minimum)\b",
                        r"\b(bulan|tahun|periode|waktu)\b.*\b(ini|lalu|kemarin)\b",
                        r"\b(kategori|brand|region|wilayah|produk)\b",
                    ],
                    "weight": 1.0,
                    "description": "Data query indicators"
                },
                {
                    "patterns": [
                        r"\b(select|from|where|group by|order by)\b",
                        r"\b(database|tabel|kolom)\b",
                    ],
                    "weight": 1.5,
                    "description": "SQL-related terms"
                }
            ],
            IntentType.SUMMARY: [
                {
                    "patterns": [
                        r"\b(ringkas|summary|rangkum|kesimpulan|overview)\b",
                        r"\b(jelaskan|explain|apa itu|what is)\b",
                        r"\b(tabel|table|markdown|md)\b",
                    ],
                    "weight": 1.5,  # Increased weight for better detection
                    "description": "Summary request indicators"
                }
            ],
            IntentType.GREETING: [
                {
                    "patterns": [
                        r"\b(halo|hai|hello|hi|selamat)\b",
                        r"\b(pagi|siang|sore|malam)\b",
                        r"\b(apa kabar|how are you)\b",
                    ],
                    "weight": 1.0,
                    "description": "Greeting patterns"
                }
            ],
            IntentType.HELP: [
                {
                    "patterns": [
                        r"\b(help|bantuan|tolong|gimana|bagaimana)\b",
                        r"\b(cara|how to|tutorial)\b",
                        r"\b(bisa|dapat|could|can)\b.*\b(apa|what)\b",
                    ],
                    "weight": 1.0,
                    "description": "Help request patterns"
                }
            ],
            IntentType.CLARIFICATION: [
                {
                    "patterns": [
                        r"\b(maksud|mean|artinya|maksudnya)\b",
                        r"\b(tidak mengerti|don't understand|bingung)\b",
                        r"\b(ulang|repeat|lagi)\b",
                    ],
                    "weight": 1.0,
                    "description": "Clarification request patterns"
                }
            ],
            IntentType.FOLLOW_UP: [
                {
                    "patterns": [
                        r"\b(lalu|then|selanjutnya|next)\b",
                        r"\b(bagaimana dengan|what about|dan)\b",
                        r"\b(juga|also|too|serta)\b",
                    ],
                    "weight": 0.8,
                    "description": "Follow-up indicators"
                }
            ]
        }
    
    def _initialize_routing_rules(self) -> List[RoutingRule]:
        """Initialize routing rules."""
        return [
            RoutingRule(IntentType.QUERY, "query", priority=10),
            RoutingRule(IntentType.SUMMARY, "summary", priority=10),
            RoutingRule(IntentType.GREETING, "greeting", priority=5),
            RoutingRule(IntentType.HELP, "help", priority=5),
            RoutingRule(IntentType.CLARIFICATION, "clarification", priority=5),
            RoutingRule(IntentType.FOLLOW_UP, "query", priority=8),  # Follow-ups usually need data
            RoutingRule(IntentType.UNKNOWN, "query", priority=1),  # Default fallback
        ]
    
    def classify_intent(
        self,
        user_input: str,
        context: Optional[List[ConversationTurn]] = None
    ) -> IntentClassification:
        """
        Classify user intent with confidence scoring.
        
        Args:
            user_input: User's input text
            context: Optional conversation context
            
        Returns:
            Intent classification result
        """
        try:
            user_input_lower = user_input.lower()
            intent_scores = {}
            
            # Score each intent type
            for intent_type, pattern_groups in self.intent_patterns.items():
                score = 0.0
                matched_patterns = []
                
                for pattern_group in pattern_groups:
                    group_score = 0.0
                    for pattern in pattern_group["patterns"]:
                        if re.search(pattern, user_input_lower, re.IGNORECASE):
                            group_score += pattern_group["weight"]
                            matched_patterns.append(pattern)
                    
                    score += group_score
                
                if score > 0:
                    intent_scores[intent_type] = {
                        "score": score,
                        "patterns": matched_patterns
                    }
            
            # Apply context-based adjustments
            if context and self.memory_manager:
                intent_scores = self._apply_context_adjustments(intent_scores, context, user_input)
            
            # Determine best intent
            if not intent_scores:
                return IntentClassification(
                    intent=IntentType.UNKNOWN,
                    confidence=0.0,
                    reasoning="No patterns matched"
                )
            
            best_intent = max(intent_scores.keys(), key=lambda x: intent_scores[x]["score"])
            best_score = intent_scores[best_intent]["score"]
            
            # Normalize confidence (simple approach)
            max_possible_score = sum(
                sum(pg["weight"] for pg in pattern_groups)
                for pattern_groups in self.intent_patterns.values()
            )
            confidence = min(best_score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0
            
            reasoning = f"Matched patterns: {intent_scores[best_intent]['patterns']}"
            
            return IntentClassification(
                intent=best_intent,
                confidence=confidence,
                reasoning=reasoning,
                metadata={"all_scores": {k.value: v["score"] for k, v in intent_scores.items()}}
            )
            
        except Exception as e:
            self.logger.error(f"Intent classification failed: {e}")
            return IntentClassification(
                intent=IntentType.UNKNOWN,
                confidence=0.0,
                reasoning=f"Classification error: {str(e)}"
            )
    
    def _apply_context_adjustments(
        self,
        intent_scores: Dict[IntentType, Dict[str, Any]],
        context: List[ConversationTurn],
        user_input: str
    ) -> Dict[IntentType, Dict[str, Any]]:
        """Apply context-based adjustments to intent scores."""
        if not context:
            return intent_scores
        
        # Get last turn
        last_turn = context[-1] if context else None
        
        # Boost follow-up intent if previous turn was a query
        if last_turn and last_turn.intent == "query":
            # Check for follow-up indicators
            follow_up_indicators = ["lalu", "then", "selanjutnya", "bagaimana dengan", "dan"]
            if any(indicator in user_input.lower() for indicator in follow_up_indicators):
                if IntentType.FOLLOW_UP in intent_scores:
                    intent_scores[IntentType.FOLLOW_UP]["score"] *= 1.5
                else:
                    intent_scores[IntentType.FOLLOW_UP] = {"score": 1.0, "patterns": ["context_boost"]}
        
        # Boost summary intent if there's recent data
        if last_turn and last_turn.sql_query:
            summary_indicators = ["ringkas", "summary", "jelaskan"]
            if any(indicator in user_input.lower() for indicator in summary_indicators):
                if IntentType.SUMMARY in intent_scores:
                    intent_scores[IntentType.SUMMARY]["score"] *= 1.3
        
        return intent_scores
    
    def route_request(
        self,
        user_input: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Tuple[str, IntentClassification]:
        """
        Route request to appropriate handler.
        
        Args:
            user_input: User's input text
            session_id: Optional session identifier
            user_id: Optional user identifier
            
        Returns:
            Tuple of (target_node, intent_classification)
        """
        try:
            # Get conversation context if available
            context = []
            if session_id and self.memory_manager:
                context, _ = self.memory_manager.get_relevant_context(session_id, user_input)
            
            # Classify intent
            classification = self.classify_intent(user_input, context)
            
            # Find matching routing rule
            target_node = self._find_target_node(classification)
            
            # Apply fallback if confidence is too low
            if classification.confidence < self.confidence_threshold:
                self.logger.warning(
                    f"Low confidence ({classification.confidence:.2f}) for intent {classification.intent.value}, "
                    f"using fallback route: {self.default_route}"
                )
                target_node = self.default_route
                classification.metadata["fallback_applied"] = True
            
            self.logger.info(
                f"Routed to '{target_node}' (intent: {classification.intent.value}, "
                f"confidence: {classification.confidence:.2f})"
            )
            
            return target_node, classification
            
        except Exception as e:
            self.logger.error(f"Routing failed: {e}")
            # Emergency fallback
            fallback_classification = IntentClassification(
                intent=IntentType.UNKNOWN,
                confidence=0.0,
                reasoning=f"Routing error: {str(e)}"
            )
            return self.default_route, fallback_classification
    
    def _find_target_node(self, classification: IntentClassification) -> str:
        """Find target node based on intent classification."""
        # Sort rules by priority (highest first)
        sorted_rules = sorted(
            [rule for rule in self.routing_rules if rule.enabled],
            key=lambda x: x.priority,
            reverse=True
        )
        
        # Find first matching rule
        for rule in sorted_rules:
            if rule.intent == classification.intent:
                # Check additional conditions if any
                if self._check_rule_conditions(rule, classification):
                    return rule.target_node
        
        # Fallback to default
        return self.default_route
    
    def _check_rule_conditions(
        self,
        rule: RoutingRule,
        classification: IntentClassification
    ) -> bool:
        """Check if routing rule conditions are met."""
        if not rule.conditions:
            return True
        
        # Add condition checking logic here if needed
        # For now, return True (no additional conditions)
        return True
    
    def add_routing_rule(self, rule: RoutingRule) -> None:
        """Add a new routing rule."""
        self.routing_rules.append(rule)
        self.routing_rules.sort(key=lambda x: x.priority, reverse=True)
        self.logger.info(f"Added routing rule: {rule.intent.value} -> {rule.target_node}")
    
    def update_confidence_threshold(self, threshold: float) -> None:
        """Update confidence threshold for fallback routing."""
        if 0.0 <= threshold <= 1.0:
            self.confidence_threshold = threshold
            self.logger.info(f"Updated confidence threshold to {threshold}")
        else:
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics and configuration."""
        return {
            "routing_rules": len(self.routing_rules),
            "enabled_rules": len([r for r in self.routing_rules if r.enabled]),
            "confidence_threshold": self.confidence_threshold,
            "default_route": self.default_route,
            "intent_types": [intent.value for intent in IntentType],
        }
