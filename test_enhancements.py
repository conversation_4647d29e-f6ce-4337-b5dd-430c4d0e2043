#!/usr/bin/env python3
"""
Comprehensive test script for the AI agent enhancements.
Tests error handling, memory management, and advanced routing.
"""

import asyncio
import json
import sys
import traceback
from datetime import datetime
from typing import Dict, Any

# Add the app directory to the path
sys.path.insert(0, '.')

from app.core.error_handler import <PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>, error_handler
from app.core.memory_manager import memory_manager, ConversationTurn, UserPreferences
from app.core.router import AdvancedRouter, IntentType
from app.core.retry_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, RetryConfig
from app.agent.graph import build_graph


class TestRunner:
    """Test runner for agent enhancements."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.test_results = []
    
    def run_test(self, test_name: str, test_func):
        """Run a single test and record results."""
        print(f"\n🧪 Testing: {test_name}")
        try:
            test_func()
            print(f"✅ PASSED: {test_name}")
            self.passed += 1
            self.test_results.append({"test": test_name, "status": "PASSED", "error": None})
        except Exception as e:
            print(f"❌ FAILED: {test_name} - {str(e)}")
            print(f"   Traceback: {traceback.format_exc()}")
            self.failed += 1
            self.test_results.append({"test": test_name, "status": "FAILED", "error": str(e)})
    
    def print_summary(self):
        """Print test summary."""
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.failed > 0:
            print(f"\n❌ Failed tests:")
            for result in self.test_results:
                if result["status"] == "FAILED":
                    print(f"   - {result['test']}: {result['error']}")


def test_error_handling():
    """Test error handling framework."""
    # Test AgentError creation
    error = AgentError(
        message="Test error",
        error_code=ErrorCode.VALIDATION_ERROR,
        details={"test": True}
    )
    assert error.error_code == ErrorCode.VALIDATION_ERROR
    assert error.details["test"] is True
    print(f"   Debug: User message = '{error.user_message}'")
    assert "validasi" in error.user_message.lower() or "validation" in error.user_message.lower()
    
    # Test error handler
    response = error_handler.handle_error(error)
    assert response["success"] is False
    assert "error" in response
    assert response["error"]["error_code"] == ErrorCode.VALIDATION_ERROR.value
    
    # Test success response
    success = error_handler.create_success_response({"data": "test"})
    assert success["success"] is True
    assert success["data"]["data"] == "test"
    
    print("   ✓ Error handling framework working correctly")


def test_retry_mechanism():
    """Test retry mechanism."""
    retry_handler = RetryHandler(RetryConfig(max_attempts=3, base_delay=0.1))
    
    # Test successful execution
    def success_func():
        return "success"
    
    result = retry_handler.execute_with_retry(success_func)
    assert result == "success"
    
    # Test retry on failure
    attempt_count = 0
    def failing_func():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise ConnectionError("Test connection error")
        return "success_after_retry"
    
    result = retry_handler.execute_with_retry(failing_func)
    assert result == "success_after_retry"
    assert attempt_count == 3
    
    print("   ✓ Retry mechanism working correctly")


def test_memory_management():
    """Test memory management system."""
    # Test conversation turn storage
    turn_id = memory_manager.store_conversation_turn(
        session_id="test_session_123",
        user_id="test_user_456",
        user_input="Test query about sales data",
        agent_response="Here is your sales data analysis",
        intent="query",
        sql_query="SELECT * FROM sales",
        metadata={"test": True}
    )
    
    if memory_manager.memory_enabled:
        assert turn_id != "memory_disabled"
        
        # Test conversation history retrieval
        history = memory_manager.get_conversation_history("test_session_123", limit=5)
        assert len(history) >= 1
        assert history[0].user_input == "Test query about sales data"
        assert history[0].intent == "query"
    
    # Test user preferences
    prefs = UserPreferences(
        user_id="test_user_456",
        preferred_language="id",
        preferred_format="summary"
    )
    
    success = memory_manager.store_user_preferences(prefs)
    if memory_manager.memory_enabled:
        assert success is True
        
        retrieved_prefs = memory_manager.get_user_preferences("test_user_456")
        assert retrieved_prefs is not None
        assert retrieved_prefs.preferred_language == "id"
    
    print("   ✓ Memory management working correctly")


def test_advanced_routing():
    """Test advanced routing system."""
    router = AdvancedRouter()
    
    # Test intent classification
    test_cases = [
        ("Tampilkan data penjualan bulan ini", IntentType.QUERY),
        ("Ringkas data yang sudah ada", IntentType.SUMMARY),
        ("Halo, selamat pagi", IntentType.GREETING),
        ("Bagaimana cara menggunakan sistem ini?", IntentType.HELP),
        ("Apa maksudnya?", IntentType.CLARIFICATION),
    ]
    
    for input_text, expected_intent in test_cases:
        classification = router.classify_intent(input_text)
        assert classification.intent == expected_intent, f"Expected {expected_intent}, got {classification.intent} for '{input_text}'"
        assert 0.0 <= classification.confidence <= 1.0
    
    # Test routing
    target, classification = router.route_request("Tampilkan data penjualan")
    assert target in ["query", "summary", "greeting", "help", "clarification", "follow_up", "unknown"]
    assert classification.intent in IntentType
    
    print("   ✓ Advanced routing working correctly")


def test_graph_integration():
    """Test integrated graph with all enhancements."""
    try:
        graph = build_graph()
        assert graph is not None
        
        # Test state processing
        test_state = {
            "input": "Tampilkan data penjualan bulan ini",
            "user_id": "test_user",
            "session_id": "test_session",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # This would normally execute the full graph, but we'll just test compilation
        print("   ✓ Graph builds successfully with all enhancements")
        
    except Exception as e:
        raise AssertionError(f"Graph integration failed: {str(e)}")


def test_input_validation():
    """Test input validation."""
    from app.core.error_handler import validate_input
    
    # Test valid input
    try:
        validate_input(
            {"name": "test", "age": 25},
            required_fields=["name"],
            field_types={"name": str, "age": int}
        )
    except AgentError:
        raise AssertionError("Valid input should not raise error")
    
    # Test missing required field
    try:
        validate_input(
            {"age": 25},
            required_fields=["name"],
            field_types={"name": str}
        )
        raise AssertionError("Should have raised error for missing field")
    except AgentError as e:
        assert e.error_code == ErrorCode.MISSING_REQUIRED_FIELD
    
    # Test wrong type
    try:
        validate_input(
            {"name": 123},
            required_fields=["name"],
            field_types={"name": str}
        )
        raise AssertionError("Should have raised error for wrong type")
    except AgentError as e:
        assert e.error_code == ErrorCode.VALIDATION_ERROR
    
    print("   ✓ Input validation working correctly")


def main():
    """Run all tests."""
    print("🚀 Starting comprehensive test suite for AI agent enhancements")
    print(f"Timestamp: {datetime.utcnow().isoformat()}")
    
    runner = TestRunner()
    
    # Run all tests
    runner.run_test("Error Handling Framework", test_error_handling)
    runner.run_test("Retry Mechanism", test_retry_mechanism)
    runner.run_test("Memory Management", test_memory_management)
    runner.run_test("Advanced Routing", test_advanced_routing)
    runner.run_test("Graph Integration", test_graph_integration)
    runner.run_test("Input Validation", test_input_validation)
    
    # Print summary
    runner.print_summary()
    
    # Exit with appropriate code
    sys.exit(0 if runner.failed == 0 else 1)


if __name__ == "__main__":
    main()
