import asyncio
import signal
import uvicorn
from fastapi import FastAPI
from app.http_handler import router as http_router
from app.config import APP_PORT

app = FastAPI()
app.include_router(http_router)

stop_event = asyncio.Event()


def handle_shutdown():
    print("Shutting down gracefully...")
    stop_event.set()


def setup_signal_handlers():
    loop = asyncio.get_event_loop()
    loop.add_signal_handler(signal.SIGINT, handle_shutdown)
    loop.add_signal_handler(signal.SIGTERM, handle_shutdown)


if __name__ == "__main__":
    setup_signal_handlers()
    uvicorn.run("main:app", host="0.0.0.0", port=APP_PORT)
